import { DetailsContainer, MyTable, StyledDataTable } from "../../organisms";
import { memo, useEffect, useMemo, useState, useTransition } from "react";
import { getInitialTableFilters } from "../../../utils/functions/getInitialTableFilters";
import { Dropdown, Flex } from "antd";
import {
  deepClone,
  getAttributeTitleWidth,
  getParentID,
  transformObjectPath,
} from "../../../utils";
import { getNodeDetails } from "../../../services/node";
import { AttributeItem } from "..";
import { LoadingOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { styled } from "@linaria/react";
import {
  useHyperlinkActions,
  usePermissions,
} from "../../../utils/functions/customHooks";
import { IAttributes, INodeDetails, ITemplates } from "../../../interfaces";
import { useSearchParams } from "react-router-dom";
import { useQueryClient } from "react-query";
import {
  ATTACHMENT_NODE_ID,
  GET_NODE_ATTRIBUTES_DETAILS,
  getAttributeIcon,
  PERMISSIONS_NODE_ID,
} from "../../../constants";
import { NoPermissionsModal } from "../../molecules";

const TITLE_CLASSNAME = "hyperlinktables-title";

const baseUrl =
  import.meta.env.VITE_APP_BASE_URL === "/"
    ? ""
    : import.meta.env.VITE_APP_BASE_URL;

interface Props {
  val: any;
  className?: string;
  id?: string;
}

const RelationComponent = ({ val, className, id }: Props) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();
  const { getPermissions } = usePermissions();
  const [noPermissionPopup, setNoPermissionPopup] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, startTransition] = useTransition();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [attributeLookupLoading, setAttributeLookupLoading] = useState(false);
  const [triggerChange, setTriggerChange] = useState(0);
  const [attributeLookup, setAttributeLookup] = useState(null);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [expandedRows, setExpandedRows] = useState(null);
  const [columns, setColumns] = useState([]);
  const mask = useSelector((root: RootState) => root.sidebar.mask);
  const [filters, setFilters] = useState(
    getInitialTableFilters([
      { key: "name" },
      {
        key: "templateId",
      },
      {
        key: "path",
      },
    ])
  );

  const [sort, setSort] = useState({
    field: null,
    order: null,
  });

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const HYPERLINKS_ACTIONS_TRASH = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("View in trashcan"),
      key: "view-in-trashcan",
    },
  ];

  const HYPERLINKS_ACTIONS = [
    {
      label: t("View Details"),
      key: "details",
    },
    {
      label: t("Open in new tab"),
      key: "open-in-new-tab",
    },
  ];

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const parentID = await getParentID(id);
        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const selectedAttribute = useMemo(() => {
    if (searchParams?.get("template")) {
      const selectedTemplate =
        templatesData[Number(searchParams?.get("template"))];
      const selectedAttribute = selectedTemplate?.attributeTemplates?.find(
        (attr) => attr.id === id
      );

      return selectedAttribute;
    }
    const nodeDetails = queryClient.getQueryData([
      GET_NODE_ATTRIBUTES_DETAILS,
      searchParams.get("nodeId"),
    ]) as INodeDetails;
    const selectedTemplate = templatesData[nodeDetails?.templateId];
    const selectedAttribute = selectedTemplate?.attributeTemplates?.find(
      (attr) => attr.id === id
    );

    return selectedAttribute;
  }, [searchParams, templatesData, id]);

  const isSimpleVisualization = data?.length < 2;

  useEffect(() => {
    setLoading(true);
  }, [val]);

  useEffect(() => {
    if (isSimpleVisualization) {
      setColumns([
        {
          label: t("Asset Name"),
          key: "name",
          render: (record) => {
            return (
              <Flex vertical gap={8}>
                <Flex
                  justify="space-between"
                  wrap="wrap"
                  align="center"
                  gap={10}
                >
                  {expandedRows && expandedRows[record?.id] ? (
                    <Dropdown
                      menu={{
                        items: record?.inTrash
                          ? HYPERLINKS_ACTIONS_TRASH
                          : HYPERLINKS_ACTIONS,
                        onClick: (e) => {
                          handleNodeClick(e.key, record.id, record.name);
                        },
                      }}
                      trigger={["contextMenu"]}
                    >
                      <div
                        className={`title-container ${
                          record.inTrash ? "trash-hyperlink" : ""
                        }`}
                        onClick={async (e) => {
                          e.stopPropagation();
                          handleHyperlinkAction({
                            id: record.id,
                            inTrash: record.inTrash,
                          });
                        }}
                      >
                        {record.name}
                      </div>
                    </Dropdown>
                  ) : record?.attributeLookup?.length > 0 ? (
                    <div className="attribute-lookup">
                      <AttributeItem
                        readOnly
                        noTitle
                        value={record?.attributeLookup[0]?.value}
                        type={record?.attributeLookup[0]?.type}
                        key={record?.attributeLookup[0].id}
                        title={record?.attributeLookup[0].name}
                        titleClassName={TITLE_CLASSNAME}
                      />
                    </div>
                  ) : (
                    <Dropdown
                      menu={{
                        items: record?.inTrash
                          ? HYPERLINKS_ACTIONS_TRASH
                          : HYPERLINKS_ACTIONS,
                        onClick: (e) => {
                          handleNodeClick(e.key, record.id, record.name);
                        },
                      }}
                      trigger={["contextMenu"]}
                    >
                      <div
                        className={`title-container ${
                          record.inTrash ? "trash-hyperlink" : ""
                        }`}
                        onClick={async (e) => {
                          e.stopPropagation();
                          handleHyperlinkAction({
                            id: record.id,
                            inTrash: record.inTrash,
                          });
                        }}
                      >
                        {record.name}
                      </div>
                    </Dropdown>
                  )}

                  <p>
                    {record?.pathName
                      ? transformObjectPath(record?.pathName, record.inTrash)
                      : "-"}
                  </p>
                </Flex>

                {/* {!Object.keys(expandedRows || {})?.includes(
                  record?.id.toString()
                ) &&
                  record?.attributeLookup?.map((lookup) => (
                    <AttributeItem
                      readOnly
                      value={lookup?.value}
                      type={lookup?.type}
                      key={lookup.id}
                      title={lookup.name}
                      titleClassName={TITLE_CLASSNAME}
                    />
                  ))} */}
              </Flex>
            );
          },
        },
      ]);
    } else {
      setColumns([
        {
          headerName: attributeLookup || t("Asset Name"),
          field: "name",
          flex: 1,
          minWidth: 200,
          cellRenderer: "agGroupCellRenderer",
          cellRendererParams: {
            innerRenderer: (params) => {
              const record = params?.data;
              const attributeLookup = params?.data?.attributeLookup;

              return (
                <Flex vertical gap={8}>
                  {attributeLookup && attributeLookup?.length > 0 ? (
                    params?.node?.expanded ? (
                      <Dropdown
                        menu={{
                          items: record?.inTrash
                            ? HYPERLINKS_ACTIONS_TRASH
                            : HYPERLINKS_ACTIONS,
                          onClick: (e) =>
                            handleNodeClick(e.key, record.id, record.name),
                        }}
                        trigger={["contextMenu"]}
                      >
                        <p
                          className={`title-container ${
                            record.inTrash ? "trash-hyperlink" : ""
                          }`}
                          onClick={async (e) => {
                            e.stopPropagation();
                            handleHyperlinkAction({
                              id: record.id,
                              inTrash: record.inTrash,
                            });
                          }}
                        >
                          {record.name}
                        </p>
                      </Dropdown>
                    ) : (
                      <div className="attribute-lookup">
                        <AttributeItem
                          readOnly
                          noTitle
                          value={attributeLookup[0]?.value}
                          type={attributeLookup[0]?.type}
                          key={attributeLookup[0].id}
                          title={attributeLookup[0].name}
                          titleClassName={TITLE_CLASSNAME}
                        />
                      </div>
                    )
                  ) : (
                    <Dropdown
                      menu={{
                        items: record?.inTrash
                          ? HYPERLINKS_ACTIONS_TRASH
                          : HYPERLINKS_ACTIONS,
                        onClick: (e) =>
                          handleNodeClick(e.key, record.id, record.name),
                      }}
                      trigger={["contextMenu"]}
                    >
                      <p
                        className={`title-container ${
                          record.inTrash ? "trash-hyperlink" : ""
                        }`}
                        onClick={async (e) => {
                          e.stopPropagation();
                          handleHyperlinkAction({
                            id: record.id,
                            inTrash: record.inTrash,
                          });
                        }}
                      >
                        {record.name}
                      </p>
                    </Dropdown>
                  )}
                </Flex>
              );
            },
          },
        },
        {
          headerName: "Object Template",
          field: "templateId",
          minWidth: 100,
          flex: 1,
          cellRenderer: (event) => {
            const record = event?.data;

            const selectedTemplate = templatesData[Number(record?.templateId)];
            if (!selectedTemplate) {
              return "-";
            }
            const templateIcon = selectedTemplate?.icon || "_30_folder";

            return (
              <p className="title-container">
                {getAttributeIcon(templateIcon)}
                {selectedTemplate.name}
              </p>
            );
          },
        },
        {
          headerName: "Path",
          field: "pathName",
          minWidth: 200,
          flex: 1,
          cellRenderer: ({ data }) => (
            <p className="right-align">
              {data?.pathName
                ? transformObjectPath(data?.pathName, data.inTrash)
                : "-"}
            </p>
          ),
        },
      ]);
    }
    // Remove setTriggerChange to prevent unnecessary re-renders
  }, [val, isSimpleVisualization, mask]);

  useEffect(() => {
    const titles = document.querySelectorAll(`.${TITLE_CLASSNAME}`) as any;

    titles.forEach((title) => {
      title.style.width = `fit-content`;
    });

    const maxTitleWidth = getAttributeTitleWidth(`.${TITLE_CLASSNAME}`);
    titles.forEach((title) => {
      title.style.width = `${maxTitleWidth}px`;
    });
  }, [data]);

  const rowExpansionTemplate = (data) => {
    if (!data?.attributes) {
      return <LoadingOutlined style={{ fontSize: 24, marginLeft: 15 }} />;
    }

    if (data?.attributes?.length === 0) {
      if (data?.hasNewAttributes) {
        return (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        );
      }
      return <p>{t("No attributes11")}</p>;
    }

    return (
      <div style={{ marginLeft: 15 }}>
        {data?.attributes?.map((attribute) => (
          <AttributeItem
            readOnly
            key={attribute.id}
            {...attribute}
            title={attribute.name}
            titleClassName={TITLE_CLASSNAME}
          />
        ))}

        {data?.hasNewAttributes && (
          <div className="new-attributes">
            <p>{t("New attributes exists")}</p>
          </div>
        )}
      </div>
    );
  };

  const handleNodeExpand = async (id) => {
    const attributes = [];
    const nodeDetails = await getNodeDetails(id);
    const selectedTemplateAttributes =
      templatesData[Number(nodeDetails.templateId)]?.attributeTemplates || [];
    const nodeAttributes = nodeDetails?.body?.filter(
      (attr) =>
        attr.id !== PERMISSIONS_NODE_ID && attr.id !== ATTACHMENT_NODE_ID
    );
    const hasNewAttributes =
      selectedTemplateAttributes?.length > nodeAttributes?.length;

    selectedTemplateAttributes?.forEach((attribute: IAttributes) => {
      const attributeValue = nodeDetails?.body?.find(
        (item) => item.id == attribute.id
      );
      if (attributeValue) {
        attributes.push({
          ...attributeValue,
          ...attribute,
          value:
            attribute.type === "multiplicity"
              ? {
                  text1: attributeValue?.value?.split("..")[0],
                  text2: attributeValue?.value?.split("..")[1],
                }
              : attribute.type === "switch"
              ? attributeValue?.value || false
              : attributeValue?.value,
        });
      }
    });

    const updatedData = [...data];
    const index = updatedData.findIndex((item) => item.id === id);
    updatedData[index].attributes = [...attributes];
    updatedData[index].hasNewAttributes = hasNewAttributes || false;
    setData([...updatedData]);
  };

  const generateAttributeLookup = async (attributeLookup) => {
    const selectedTemplate = templatesData[
      Object.keys(attributeLookup)[0]
    ] as ITemplates;
    // Create a deep copy of val to avoid mutation
    const newValue = deepClone(val);

    const newRelationsWithLookup = await Promise.all(
      newValue.map(async (relation) => {
        let nodeDetails = queryClient.getQueryData([
          GET_NODE_ATTRIBUTES_DETAILS,
          relation?.id,
        ]) as INodeDetails;

        if (!nodeDetails) {
          nodeDetails = await getNodeDetails(relation?.id);
        }

        const lookupValue = [];
        const attrLookup = attributeLookup[nodeDetails?.templateId];

        attrLookup?.forEach((attr) => {
          const selected = nodeDetails?.body?.find((att) => att.id === attr);

          // Make sure to push a new object to avoid mutating the original
          if (selected) {
            lookupValue.push({ ...selected }); // Spread operator creates a shallow copy
          } else {
            const selectedTemplateAttribute =
              selectedTemplate?.attributeTemplates?.find(
                (_attr) => _attr.id === attr
              );
            lookupValue.push({ ...selectedTemplateAttribute, value: null });
          }
        });

        // Check if the template has attributes
        const hasAttributes =
          templatesData[nodeDetails?.templateId]?.attributeTemplates?.length >
          0;

        return {
          ...relation, // Spread to ensure a new object is returned
          attributeLookup: lookupValue,
          templateHasAttributes: hasAttributes, // Set templateHasAttributes based on template's attributeTemplates
        };
      })
    );

    // Merge newRelationsWithLookup with existing data
    setData(newRelationsWithLookup);
    setAttributeLookupLoading(false);
    setLoading(false);
  };

  useEffect(() => {
    setAttributeLookupLoading(true);
    if (selectedAttribute?.attributeLookup) {
      // Use startTransition if needed for performance

      if (Object.values(selectedAttribute?.attributeLookup || {})?.length > 0) {
        const selectedTemplate = templatesData[
          Object.keys(selectedAttribute?.attributeLookup)[0]
        ] as ITemplates;
        const selectedTemplateAttribute =
          selectedTemplate?.attributeTemplates?.find(
            (attr) =>
              attr.id ===
              Object.values(selectedAttribute?.attributeLookup)[0][0]
          );
        if (selectedTemplateAttribute) {
          setAttributeLookup(selectedTemplateAttribute?.name);
        }
      }
      startTransition(() => {
        generateAttributeLookup(selectedAttribute?.attributeLookup);
      });
    } else {
      setData([...val]);
      setAttributeLookupLoading(false);
      setLoading(false);
    }
  }, [selectedAttribute?.attributeLookup, val]);

  return (
    <Wrapper className={className}>
      {/* {attributeLookupLoading && (
        <ProgressBar mode="indeterminate" style={{ height: "3px" }} />
      )} */}

      {isSimpleVisualization ? (
        <div className="simple-wrapper">
          <StyledDataTable
            globalFilterFields={["name"]}
            excelFileName="hyperlinks"
            noHeader
            lazyLoad
            filters={filters}
            setFilters={setFilters}
            sort={sort}
            setSort={setSort}
            height={"50vh"}
            columns={columns}
            data={data}
            noSelect
            virtualScroll
            // readOnlySelect
            alignLeft
            triggerChange={triggerChange}
            expandable
            expandCondition={(rowData) => {
              return rowData?.templateHasAttributes;
            }}
            onRowExpand={(e) => {
              if (e.data.id) {
                const permissions = getPermissions(e.data?.permissionsId);
                if (permissions.includes("VIEW")) {
                  const _expanded = expandedRows || {};
                  _expanded[`${e.data.id}`] = true;
                  setExpandedRows(_expanded);
                  handleNodeExpand(e.data.id);
                  // Remove setTriggerChange to prevent unnecessary re-renders
                } else {
                  setNoPermissionPopup(true);
                }
              }
            }}
            noDownload
            onRowCollapse={(e) => {
              if (e.data.id) {
                const _expanded = { ...(expandedRows || {}) };
                delete _expanded[e.data.id];
                setExpandedRows(_expanded);
                // Remove setTriggerChange to prevent unnecessary re-renders
              }
            }}
            expandedRows={expandedRows}
            rowExpansionTemplate={rowExpansionTemplate}
            // selected={data?.filter((item) => item.id)}
          />
        </div>
      ) : (
        <MyTable
          excelFileName="hyperlinks"
          columns={columns}
          data={data}
          noHeader
          loading={loading || attributeLookupLoading}
          emptyMessage="No relations"
          noSelect
          resetTrigger={triggerChange}
        />
      )}
      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}

      {noPermissionPopup && (
        <NoPermissionsModal
          visible={noPermissionPopup}
          onHide={() => {
            setNoPermissionPopup(false);
          }}
        />
      )}
    </Wrapper>
  );
};

const arePropsEqual = (prevProps, nextProps) => {
  return prevProps.val === nextProps.val;
};

const Relation = memo(RelationComponent, arePropsEqual);
export { Relation };

const Wrapper = styled.div`
  & .ag-group-value {
    width: 100%;
  }

  & .attribute-lookup .attribute-item {
    width: fit-content;
    border: none;
    margin-bottom: 0px;
  }
  /* & table {
    width: 100% !important;
  } */
  /* & th {
    display: none;
  } */

  /* & td {
    padding: 2px !important;
  } */

  & .simple-wrapper thead {
    display: none;
  }

  & .simple-wrapper .no-title > div {
    padding: 0px;
  }
  & .simple-wrapper .expandable {
    padding: 0px !important;
  }
  & .title-container {
    color: var(--color-text);
    flex: 1;

    & > .attribute-item {
      border: none;
      margin-bottom: 0px;
    }
    & img {
      object-fit: contain;
    }
  }
  & .ant-dropdown-trigger {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
`;
