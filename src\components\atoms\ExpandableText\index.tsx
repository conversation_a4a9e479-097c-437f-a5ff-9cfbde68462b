import React, { memo, useEffect } from "react";
import { styled } from "@linaria/react";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { useTextTruncation } from "../../../utils/functions/customHooks/useTextTruncation";

interface ExpandableTextProps {
  value: string;
  isHtml?: boolean;
  className?: string;
  iconSpacing?: number;
  debounceMs?: number;
  resizeDebounceMs?: number;
}

// Styled components following existing patterns
const ExpandableContainer = styled.div`
  position: relative;
  width: 100%;
  display: flex;
  align-items: flex-start;
  min-height: 100%;
`;

const TextMeasurer = styled.span`
  position: absolute;
  visibility: hidden;
  white-space: nowrap;
  font-size: 12px;
  font-family: inherit;
  top: -9999px;
  left: -9999px;
`;

const BaseContent = styled.div`
  flex: 1;
  font-size: 12px;
  line-height: 1.4;
`;

const ExpandedContent = styled(BaseContent)`
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  padding-right: 20px;
  transition: all 0.3s ease;
`;

const CollapsedContent = styled(BaseContent)<{ hasOverflow: boolean }>`
  white-space: nowrap;
  overflow: hidden;
  padding-right: ${({ hasOverflow }) => hasOverflow ? '20px' : '0'};
  transition: all 0.3s ease;
`;

const ToggleIcon = styled.span`
  position: absolute;
  display: flex;
  right: 0;
  background: #fff;
  padding: 0 5px;
  cursor: pointer;
  top: 0;
  z-index: 1;
  height: 16px;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.7;
  }
`;

/**
 * Reusable ExpandableText component with smooth transitions
 * Handles both plain text and HTML content with proper truncation
 */
const ExpandableText: React.FC<ExpandableTextProps> = memo(({
  value,
  isHtml = false,
  className = "",
  iconSpacing = 25,
  debounceMs = 100,
  resizeDebounceMs = 50,
}) => {
  const {
    isExpanded,
    isOverflowing,
    truncatedText,
    measureRef,
    containerRef,
    handleToggle,
  } = useTextTruncation(value, {
    isHtml,
    iconSpacing,
    debounceMs,
    resizeDebounceMs,
  });

  // Add class to parent ag-grid cell for proper alignment
  useEffect(() => {
    if (containerRef.current) {
      // Find the parent ag-cell element
      const agCell = containerRef.current.closest('.ag-cell');
      if (agCell) {
        agCell.classList.add('has-expandable-text');

        // Also add class to ag-cell-wrapper if it exists
        const agCellWrapper = agCell.querySelector('.ag-cell-wrapper');
        if (agCellWrapper) {
          agCellWrapper.classList.add('has-expandable-text');
        }
      }

      // Cleanup function
      return () => {
        if (agCell) {
          agCell.classList.remove('has-expandable-text');
          const agCellWrapper = agCell.querySelector('.ag-cell-wrapper');
          if (agCellWrapper) {
            agCellWrapper.classList.remove('has-expandable-text');
          }
        }
      };
    }
  }, []);

  // Don't render anything if no value
  if (!value) return null;

  return (
    <ExpandableContainer
      ref={containerRef}
      className={`expandable-text-container ${className}`}
    >
      <TextMeasurer ref={measureRef} />
      {isExpanded ? (
        isHtml ? (
          <ExpandedContent
            className="editor-content content"
            dangerouslySetInnerHTML={{ __html: value }}
          />
        ) : (
          <ExpandedContent>
            {value}
          </ExpandedContent>
        )
      ) : (
        <CollapsedContent hasOverflow={isOverflowing}>
          {truncatedText}
        </CollapsedContent>
      )}
      {isOverflowing && (
        <ToggleIcon onClick={handleToggle}>
          {isExpanded ? <UpOutlined /> : <DownOutlined />}
        </ToggleIcon>
      )}
    </ExpandableContainer>
  );
});

ExpandableText.displayName = 'ExpandableText';

export { ExpandableText };
