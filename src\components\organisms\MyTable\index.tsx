import {
  use<PERSON>allback,
  useMemo,
  useRef,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { AgGridReact } from "ag-grid-react";
import "./styles.css";
import { ColDef, GetRowIdParams } from "ag-grid-community";
import { CustomHeader } from "./CustomHeader";
import { styled } from "@linaria/react";
import { useTranslation } from "react-i18next";
import { Button } from "primereact/button";
import { Empty } from "antd";
import { localeText } from "./localeText";
import i18next from "i18next";
import { NoPermissionsModal } from "../../molecules";
import { CheckDetailsPermissions } from "./CheckDetailsPermissions";
import { EditSaveCancelRenderer } from "./EditSaveCancelRenderer";
import SwitchRenderer from "./SwitchRenderer";
import ImageRenderer from "./ImageRenderer";
import TextEditorRenderer from "./TextEditorRenderer";
import { TableLoadingSkeleton } from "./TableLoadingSkeleton";
import { uuidv4 } from "../../../utils";

export type IAGColumns = {
  headerName: string;
  type?: "date";
  field: string;
  flex?: number;
  minWidth?: number;
  cellRenderer?: any;
  isAction?: boolean;
  width?: number;
};

interface Props {
  columns: any[];
  isError?: boolean;
  onRefresh?: () => void;
  withRefresh?: boolean;
  getRowClass?: any;
  hideEmpty?: boolean;
  onDownload?: (selected) => void;
  onColumnsDelete?: (key: string) => void;
  resetTrigger?: number;
  data: any[];
  enableCellSpan?: boolean;
  noSelect?: boolean;
  defaultSelected?: any[];
  onSelect?: (values: any[]) => void;
  loading?: boolean;
  multiplicity?: string;
  fullHeight?: boolean;
  excelFileName?: string;
  displaySaveCancel?: boolean;
  onCancelClick?: () => void;
  disableSave?: boolean;
  saveLoading?: boolean;
  onSaveClick?: (newColumns: string[], filters: any, sort, pinned) => void;
  detectChange?: () => void;
  noHeader?: boolean;
  height?: string;
  setColumnsRequest?: any;
  emptyMessage?: string;
  extra?: ReactNode;
  noDownload?: boolean;
  expandCondition?: (data) => void;
  isRowSelectable?: (data) => void;
  setPinned?: (pinned) => void;
  setFilters?: (filters) => void;
  initialFilters?: any;
  setSort?: (data) => void;
  editable?: boolean;
  onRowsEdit?: (values) => void;
}

const MyTable = ({
  columns,
  data,
  enableCellSpan,
  setSort,
  noSelect,
  editable,
  onDownload,
  hideEmpty,
  isError,
  height,
  setColumnsRequest,
  fullHeight,
  onSelect,
  onColumnsDelete,
  defaultSelected,
  noDownload,
  loading,
  onRowsEdit,
  multiplicity,
  initialFilters,
  setPinned,
  displaySaveCancel,
  excelFileName,
  expandCondition,
  onCancelClick,
  noHeader,
  disableSave,
  saveLoading,
  onSaveClick,
  detectChange,
  emptyMessage,
  resetTrigger,
  getRowClass,
  extra,
  isRowSelectable,
  setFilters,
  withRefresh,
  onRefresh,
}: Props) => {
  const [selected, setSelected] = useState([]);
  const gridRef = useRef<AgGridReact>(null);
  const { t } = useTranslation();
  const [noPermissionPopup, setNoPermissionPopup] = useState(null);

  const [initialLoad, setInitialLoad] = useState(true);

  // Memoized callback for setting no permission popup to maintain stable reference
  const stableSetNoPermissionPopup = useCallback((node) => {
    setNoPermissionPopup(node);
  }, []);

  const [columnDefs, setColumnDefs] = useState<ColDef[]>([]);

  const memoizedData = useMemo(
    () => (data || []).map((d) => ({ ...d, uuid: uuidv4() })),
    [data]
  );

  const editableRowData = useMemo(() => {
    if (!editable || !memoizedData) return memoizedData;
    return memoizedData.map((item, index) => ({ ...item, index }));
  }, [editable, memoizedData]);

  const optimalRowBuffer = useMemo(() => {
    const dataLength = data?.length || 0;
    if (dataLength > 10000) return 100;
    if (dataLength > 1000) return 75;
    if (dataLength > 100) return 50;
    return 20;
  }, [data?.length]);

  const tableHeight = useMemo(() => {
    if (fullHeight) return "100%";
    if (height) return height;
    if (loading || data?.length === 0) return 200;
    if (data?.length > 5) return 300;
    return "100%";
  }, [fullHeight, height, loading, data?.length]);

  const isRowMaster = useCallback(
    (data: any) =>
      data?.templateHasAttributes || (expandCondition && expandCondition(data)),
    [expandCondition]
  );
  const gridOptions = useMemo(
    () => ({
      masterDetail: true,
      context: {
        setNoPermissionPopup: stableSetNoPermissionPopup,
      },
      detailCellRenderer: CheckDetailsPermissions,
      noRowsOverlayComponent: () => <p>{emptyMessage || "No data"}</p>,
      // Use our custom loading overlay component
      loadingOverlayComponent: TableLoadingSkeleton,
      // Don't show the loading overlay - we'll handle it ourselves
      suppressLoadingOverlay: true,
      ensureDomOrder: false,
      suppressColumnVirtualisation: false,
      suppressRowVirtualisation: false,
      debounceVerticalScrollbar: true,
      rowHeight: 42,
    }),
    [emptyMessage, stableSetNoPermissionPopup]
  );

  const getRowId = useCallback(
    (params: GetRowIdParams) => String(params.data.uuid),
    []
  );

  // Process columns more efficiently
  const processColumns = useCallback(
    (_columns: any[]) => {
      if (!_columns || _columns.length === 0) return [];

      return _columns
        .map((_column: any) => {
          if (_column?.isEditActions) {
            return {
              field: "actions",
              headerName: "",
              isAction: true,
              width: 100,
              cellRenderer: EditSaveCancelRenderer,
              singleClickEdit: true,
              sortable: false,
              headerComponent: null,
              filter: false,
            };
          }

          return {
            ..._column,
            sortable: _column?.isAction ? false : true,
            autoHeight: true,
            cellEditorSelector: (params: any) => {
              if (params.value === "true" || params.value === "false") {
                return {
                  component: SwitchRenderer,
                  params: { ...params },
                };
              }
              if (params?.data?.editType === "image") {
                return {
                  component: ImageRenderer,
                  params: { ...params },
                };
              }
              if (params?.data?.editType === "textEditor") {
                return {
                  popup: true,
                  component: TextEditorRenderer,
                  params: {
                    ...params,
                  },
                };
              }
              return {
                component: "agTextCellEditor",
              };
            },
            headerName: t(_column.headerName),
            headerComponent: _column?.headerComponent
              ? _column.headerComponent
              : _column?.isAction
                ? null
                : (e: any) => (
                  <CustomHeader
                    {...e}
                    onColumnsDelete={() => onColumnsDelete?.(_column.field)}
                  />
                ),
            filter: _column?.isAction
              ? false
              : _column.type === "date"
                ? "agDateColumnFilter"
                : "agTextColumnFilter",
            filterParams: {
              buttons: ["reset", "apply"],
            },
          };
        })
        .filter(Boolean);
    },
    [t, onColumnsDelete]
  );

  // Update column definitions when columns or language changes
  useEffect(() => {
    const processedColumns = processColumns(columns);
    setColumnDefs(processedColumns);
  }, [columns, i18next.language, processColumns]);

  useEffect(() => {
    if (!initialLoad || !gridRef?.current?.api || columnDefs?.length === 0) {
      return;
    }

    try {
      gridRef?.current?.api?.setFilterModel(initialFilters);
      setInitialLoad(false);
    } catch {
      setInitialLoad(false);
    }
  }, [initialFilters, initialLoad, columnDefs]);

  const defaultColDef = useMemo<ColDef>(() => {
    return {
      unSortIcon: true,
      filter: true,
      // Set a global minimum width for all columns
      // Avoid columns being too narrow to read when resizing
      minWidth: 80,
    };
  }, []);

  const rowSelection = useMemo<any>(() => {
    const toMultiplicity = multiplicity?.split("..")[1];
    return {
      mode: multiplicity
        ? toMultiplicity === "1"
          ? "singleRow"
          : "multiRow"
        : "multiRow",
      headerCheckbox: multiplicity ? !(toMultiplicity !== "n") : true,
      isRowSelectable: isRowSelectable ? isRowSelectable : null,
    };
  }, [multiplicity, isRowSelectable]);

  // Safe way to check and select rows
  const safeSelectDefaultRows = useCallback(() => {
    if (!gridRef?.current?.api || !defaultSelected?.length) {
      return;
    }

    // Ensure the grid has finished data processing before selection
    setTimeout(() => {
      // First collect all nodes to avoid multiple traversals
      const allNodes = [];
      gridRef.current.api.forEachNode((node) => {
        if (node.data) allNodes.push(node);
      });

      defaultSelected.forEach((_selected) => {
        // First try to find by ID directly using the getRowNode API
        let rowNode = gridRef?.current?.api?.getRowNode(_selected.id);

        // If not found by direct ID, try to find by matching the ID in the row data
        if (!rowNode) {
          // Try to match by uuid (which is used as the row ID in getRowId)
          const matchingNodeByUuid = allNodes.find(
            (node) =>
              node.data.uuid && String(node.data.uuid) === String(_selected.id)
          );

          if (matchingNodeByUuid) {
            rowNode = matchingNodeByUuid;
          } else {
            // Try to match by the original id property in the data
            const matchingNodeById = allNodes.find(
              (node) =>
                node.data.id && String(node.data.id) === String(_selected.id)
            );

            if (matchingNodeById) {
              rowNode = matchingNodeById;
            }
          }
        }

        if (rowNode) {
          rowNode.setSelected(true);
        }
      });
    }, 50); // Small delay to ensure grid is fully rendered
  }, [defaultSelected]);

  // Handle grid ready event to ensure API is available
  const onGridReady = useCallback(() => {
    // If we have default selections and the grid is ready, apply them
    if (defaultSelected?.length > 0) {
      safeSelectDefaultRows();
    }
  }, [defaultSelected, safeSelectDefaultRows]);

  useEffect(() => {
    // Only attempt selection if both API and defaultSelected are available
    if (gridRef?.current?.api && defaultSelected?.length > 0) {
      safeSelectDefaultRows();
    }
  }, [defaultSelected, data, safeSelectDefaultRows]);

  // Safe cell refreshing
  const safeRefreshCells = useCallback(() => {
    if (gridRef?.current?.api) {
      try {
        gridRef.current.api.refreshCells({ force: true });
      } catch {
        // Silently ignore
      }
    }
  }, []);

  const exportExcel = useCallback(() => {
    if (!gridRef?.current?.api) return;

    gridRef.current.api.exportDataAsCsv({
      onlySelected: true,
      fileName: `${excelFileName}.csv`,
    });
  }, [excelFileName]);

  useEffect(() => {
    if (!resetTrigger || !gridRef?.current?.api) {
      return;
    }

    try {
      setInitialLoad(true);
      gridRef.current.api.resetColumnState();
      safeRefreshCells();
    } catch {
      // Silently ignore
    }
  }, [resetTrigger, safeRefreshCells]);

  // Enhanced save handler with better error handling
  const handleSaveClick = useCallback(() => {
    if (!gridRef?.current?.api) return;

    try {
      const api = gridRef.current.api;
      const newColumns = api.getAllGridColumns();

      const columnNames = newColumns
        .map((col) => col.getColDef().field)
        .filter(Boolean);

      const filterOptions = api.getFilterModel();
      const columnState = api.getColumnState();
      const sortModel = columnState
        .filter((col) => col.sort)
        .map(({ colId, sort }) => ({ colId, sort }));

      const pinnedColumns = newColumns
        .filter(
          (col) =>
            col.getPinned() && col.getColId() !== "ag-Grid-SelectionColumn"
        )
        .map((col) => col.getColId());

      onSaveClick?.(columnNames, filterOptions, sortModel, pinnedColumns);
    } catch {
      // Silently ignore
    }
  }, [onSaveClick]);

  // Enhanced cancel handler
  const handleCancelClick = useCallback(() => {
    onCancelClick?.();
    if (!gridRef?.current?.api) return;

    try {
      gridRef.current.api.resetColumnState();
      safeRefreshCells();
      setInitialLoad(true);
    } catch {
      // Silently ignore
    }
  }, [onCancelClick, safeRefreshCells]);

  const gridEventHandlers = useMemo(() => {
    const handleCellEditingStarted = (params: any) => {
      if (params?.data?.editType === "textEditor") {
        params.api.resetRowHeights();
        params.node.setRowHeight(300);
        params.api.onRowHeightChanged();
      }
    };

    const handleCellEditingStopped = (params: any) => {
      if (params?.data?.editType === "textEditor") {
        params.api.resetRowHeights();
        params.node.setRowHeight(42);
        params.api.onRowHeightChanged();
      }
    };

    const handleFilterChanged = (props: any) => {
      if (!props?.api) return;

      try {
        props.api.refreshHeader();
        if (props?.source === "api") return;

        const api = gridRef.current?.api;
        if (!api) return;

        const filterOptions = api.getFilterModel();
        detectChange?.();
        setFilters?.(filterOptions);
      } catch {
        // Silently ignore
      }
    };

    const handleSortChanged = (e: any) => {
      if (!e?.api || e.source !== "api") return;

      try {
        const columnState = gridRef.current.api.getColumnState();
        const sortModel = columnState
          .filter((col) => col.sort)
          .map(({ colId, sort }) => ({ colId, sort }));
        detectChange?.();
        setSort?.(sortModel);
      } catch {
        // Silently ignore
      }
    };

    const handleColumnMoved = (e: any) => {
      if (!e?.api || e.source !== "uiColumnMoved") return;

      try {
        const newColumns = gridRef.current.api.getAllGridColumns();
        const columnNames = newColumns
          .map((col) => col.getColDef().field)
          .filter(Boolean);

        setColumnsRequest?.(columnNames);
        detectChange?.();
      } catch {
        // Silently ignore
      }
    };

    const handleColumnPinned = (params: any) => {
      if (params.source !== "api") return;

      detectChange?.();

      try {
        const cols = params.api.getAllGridColumns();
        const pinnedColumns: string[] = [];

        cols?.forEach((col: any) => {
          if (col.getPinned() && col.getColId() !== "ag-Grid-SelectionColumn") {
            pinnedColumns.push(col.getColId());
          }
        });

        setPinned?.(pinnedColumns);

        if (!noSelect) {
          const hasSelectionColumn = cols.some(
            (col: any) => col.getColId() === "ag-Grid-SelectionColumn"
          );
          if (hasSelectionColumn) {
            params.api.setColumnsPinned(
              ["ag-Grid-SelectionColumn"],
              pinnedColumns?.length === 0 ? false : "left"
            );
          }
        }
      } catch {
        // Silently ignore
      }
    };

    const handleRowValueChanged = (e: any) => {
      onRowsEdit?.(e.data);
    };

    const handleSelectionChanged = (event: any) => {
      if (!event?.api || event.source === "api") return;

      try {
        const selectedNodes = event.api.getSelectedNodes();
        const selectedData = selectedNodes.map((node: any) => node.data);
        onSelect?.(selectedData);
        setSelected(selectedData);
      } catch {
        // Silently ignore
      }
    };

    const handleRowClassAssignment = (e: any) => {
      return `${getRowClass?.(e) || ""} ${
        e.data?.templateHasAttributes ? "" : "no-expand"
        }`;
    };

    return {
      handleCellEditingStarted,
      handleCellEditingStopped,
      handleFilterChanged,
      handleSortChanged,
      handleColumnMoved,
      handleColumnPinned,
      handleRowValueChanged,
      handleSelectionChanged,
      getRowClass: handleRowClassAssignment,
    };
  }, [
    detectChange,
    noSelect,
    setPinned,
    setFilters,
    setSort,
    setColumnsRequest,
    onRowsEdit,
    onSelect,
    getRowClass,
  ]);

  return (
    <Container className="my-table" onClick={(e) => e.stopPropagation()}>
      {isError ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="error-empty"
            description={t("Error in fetching data")}
          />
        </div>
      ) : !hideEmpty && !loading && data?.length === 0 ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t(emptyMessage) || t("No Data")}
          />
        </div>
      ) : (
        <>
          {!noHeader && (
            <div className="header">
              {extra}

              {withRefresh && (
                <Button
                  type="button"
                  label={t("Refresh")}
                  icon="pi pi-sync"
                  className="primary-button"
                  onClick={() => onRefresh?.()}
                  rounded
                />
              )}

              {!noDownload && (
                <Button
                  type="button"
                  label={t("Download")}
                  icon="pi pi-download"
                  className="primary-button"
                  disabled={!selected?.length}
                  onClick={
                    onDownload ? () => onDownload(selected) : exportExcel
                  }
                  rounded
                />
              )}

              {displaySaveCancel && (
                <>
                  <Button
                    rounded
                    className="primary-button cancel-button"
                    type="button"
                    onClick={handleCancelClick}
                  >
                    {t("Cancel")}
                  </Button>
                  <Button
                    rounded
                    className="primary-button save-button"
                    type="button"
                    disabled={disableSave}
                    loading={saveLoading}
                    onClick={handleSaveClick}
                  >
                    {t("Save")}
                  </Button>
                </>
              )}
            </div>
          )}
          <Wrapper
            style={{
              height: tableHeight,
              width: "100%",
            }}
          >
            {loading ? (
              <TableLoadingSkeleton />
            ) : (
              <AgGridReact
                enableCellSpan={enableCellSpan}
                defaultColDef={defaultColDef}
                ref={gridRef}
                singleClickEdit={true}
                localeText={i18next.language === "pl" ? localeText : null}
                rowBuffer={optimalRowBuffer}
                rowModelType="clientSide"
                getRowId={getRowId}
                suppressPaginationPanel={true}
                suppressHorizontalScroll={false}
                alwaysShowVerticalScroll={true}
                onGridReady={onGridReady}
                onCellEditingStarted={
                  gridEventHandlers.handleCellEditingStarted
                }
                onCellEditingStopped={
                  gridEventHandlers.handleCellEditingStopped
                }
                onFilterChanged={gridEventHandlers.handleFilterChanged}
                rowData={editableRowData}
                onSortChanged={gridEventHandlers.handleSortChanged}
                getRowClass={gridEventHandlers.getRowClass}
                onColumnMoved={gridEventHandlers.handleColumnMoved}
                // Use normal layout for proper virtualization
                domLayout="normal"
                columnDefs={columnDefs}
                onColumnPinned={gridEventHandlers.handleColumnPinned}
                rowSelection={noSelect ? undefined : rowSelection}
                headerHeight={34}
                suppressDragLeaveHidesColumns
                animateRows={false}
                loading={loading}
                isRowMaster={isRowMaster}
                detailRowAutoHeight
                editType={editable ? "fullRow" : null}
                gridOptions={gridOptions}
                pagination={true}
                paginationPageSize={100}
                onRowValueChanged={gridEventHandlers.handleRowValueChanged}
                onSelectionChanged={gridEventHandlers.handleSelectionChanged}
              />
            )}
          </Wrapper>
        </>
      )}

      {!!noPermissionPopup && (
        <NoPermissionsModal
          visible={!!noPermissionPopup}
          onHide={() => {
            setTimeout(() => {
              setNoPermissionPopup(null);
            }, 100);
          }}
        />
      )}
    </Container>
  );
};

export { MyTable };

const Container = styled.div`
  height: 100%;
  width: 100%;

  & .ag-popup-editor {
    box-shadow: none;
  }

  & .tox {
    height: 270px !important;
  }

  & .ag-cell-inline-editing {
    border: none !important;
    box-shadow: none !important;

    & input {
      height: fit-content;
      width: 90%;
      margin-left: 12px;
      flex: unset;
    }
  }

  & .ag-cell-editor {
    & input {
      box-shadow: none;
    }
  }

  & .ag-cell {
    align-items: flex-start;
    display: flex;
    line-height: unset;
  }

  & .ag-cell-wrapper {
    width: 100%;
    align-items: flex-start;
    padding-top: 8px;
    padding-bottom: 8px;
  }
  & .ag-row-hover {
    background-color: white;
    &::before {
      background-color: white;
    }
  }
  & .ag-center-cols-viewport {
    min-height: auto;
  }

  & .ag-overlay {
    min-height: 200px;
    position: relative;
    margin-top: -42px;
  }
  & .empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  & .header {
    display: flex;
    gap: 8px;
    justify-content: right;
    margin-bottom: 11px;
  }
`;
const Wrapper = styled.div`
  & .highlight-row {
    background-color: #d2e6f6;
  }

  & .no-expand .ag-row-group-indent-0 {
    margin-left: 0px;
  }
  & .expand-btn {
    cursor: pointer;
    & i {
      font-size: 15px;
    }
    &:hover i {
      color: var(--color-text);
    }
  }
  & .ag-actions {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    margin-top: 8px;
    gap: 8px;

    & .anticon-edit {
      color: var(--color-text);
    }

    & > div {
      width: 21px;
      border: 1px solid;
      height: 21px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: var(--color-text);
      border-radius: 5px;

      & > i {
        font-size: 12px;
      }
    }

    & .ag-danger {
      color: red;
    }
  }
`;
