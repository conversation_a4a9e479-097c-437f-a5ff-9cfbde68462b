import { Column } from "primereact/column";
import { DataTable, DataTableColumnResizeEndEvent } from "primereact/datatable";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button } from "primereact/button";
import { useTranslation } from "react-i18next";
import { styled } from "@linaria/react";
import { InputText } from "primereact/inputtext";
import { useTheme } from "../../../utils";
import { Skeleton } from "primereact/skeleton";
import { OverlayPanel } from "primereact/overlaypanel";
import { MultiSelect } from "primereact/multiselect";
import { IDataTableColumns, IDataTableProps } from "../../../interfaces";
import {
  DeleteOutlined,
  LoadingOutlined,
  PushpinFilled,
  PushpinOutlined,
} from "@ant-design/icons";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { InputSwitch } from "primereact/inputswitch";
import { Calendar } from "primereact/calendar";
import { Empty } from "antd";

const MyTableComponent = ({
  columns: initialColumns,
  data,
  emptyMessage,
  height,
  rowReorder,
  globalFilterFields,
  excelFileName,
  loading,
  setAllColumnsRequest,
  detectChange,
  editable,
  onRowsEdit,
  noSelect,
  extraAction,
  selectedActionsTemplate,
  selected,
  setSelected,
  triggerChange,
  singleSelection,
  displaySaveCancel,
  onSaveClick,
  onCancelClick,
  saveLoading,
  filters,
  setFilters,
  sort,
  expandable,
  expandedRows,
  rowExpansionTemplate,
  setSort,
  onRowExpand,
  onRowCollapse,
  noDownload,
  extra,
  alignLeft,
  onColumnsDelete,
  withDelete,
  onDeleteClick,
  noHeader,
  expandCondition,
  readOnlySelect,
  virtualScroll,
  hideSelectAll,
  lazyLoad,
  multiplicity,
  withMultiplicity,
  disableSelection,
  customRowClassName,
  disableSave,
  dataKey,
}: IDataTableProps) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const overlayPanelRef = useRef(null);

  // Move useSelector outside of render to prevent re-renders
  const templatesData = useSelector(
    (state: RootState) => state.templatesStore.templates
  );

  const [appliedFilters, setAppliedFilters] = useState([]);
  const [columns, setColumns] = useState([] as IDataTableColumns[]);
  // const [globalFilterValue, setGlobalFilterValue] = useState<string>("");
  // const [filters, setFilters] = useState<DataTableFilterMeta>({});
  const [key, setKey] = useState(0);
  const [initialLoad, setInitialLoad] = useState(true);
  const [showSelectedFirst] = useState(false);

  useEffect(() => {
    setInitialLoad(true);
  }, [triggerChange]);

  useEffect(() => {
    if (initialLoad) {
      setColumns(
        [...initialColumns].map((column, index) => ({
          ...column,
          index: column?.index || index,
          originalIndex: column?.originalIndex || index,
        }))
      );
      setKey(key + 1);
      const initialFilters = [];
      Object.keys(filters)?.forEach((filterKey) => {
        if (filters[filterKey]?.value) {
          initialFilters.push(filterKey);
        }
      });
      setAppliedFilters(initialFilters);

      // const initialFilters = {};
      // initialFilters["global"] = {
      //   value: null,
      //   matchMode: FilterMatchMode.CONTAINS,
      // };

      // initialColumns.forEach((column: IDataTableColumns) => {
      //   initialFilters[column.key] = {
      //     value: null,
      //     matchMode: column.isDate
      //       ? FilterMatchMode.DATE_IS
      //       : FilterMatchMode.CONTAINS,
      //   };
      // });
      // setFilters(initialFilters);
      setInitialLoad(false);
    }
  }, [initialColumns, initialLoad]);

  // function to remove from frozen
  const removeFromFrozen = (field) => {
    const updatedColumns = columns.filter((column) => column.key !== field.key);

    let toInsertIndex = null;
    columns.forEach((col, index) => {
      if (!col.frozen && toInsertIndex === null) {
        toInsertIndex = index - 1;
      }
    });

    updatedColumns.splice(
      toInsertIndex > field.originalIndex ? toInsertIndex : field.originalIndex,
      0,
      {
        ...field,
        frozen: false,
      }
    );

    const columnsRequest = updatedColumns.map((col, index) => {
      return { ...col, index };
    });

    setColumns([...columnsRequest]);
    setAllColumnsRequest && setAllColumnsRequest([...columnsRequest]);
    detectChange && detectChange();
    setKey(key + 1);
  };

  // function to freeze column and move it to left
  const addToFrozen = (field) => {
    const updatedColumns = [...columns];
    const frozenColumn = updatedColumns.splice(field.index, 1)[0];
    updatedColumns.unshift({
      ...frozenColumn,
      frozen: true,
    });
    const columnsRequest = [
      ...updatedColumns.map((col, index) => {
        return { ...col, index };
      }),
    ];
    setColumns(columnsRequest);
    setAllColumnsRequest && setAllColumnsRequest(columnsRequest);
    detectChange && detectChange();
    setKey(key + 1);
  };

  // custom column header
  const getColumnHeader = (field) => {
    return (
      <div className="column-header" style={{ width: field.width }}>
        {t(field.label)}
        <Button
          type="button"
          icon={field.frozen ? <PushpinFilled /> : <PushpinOutlined />}
          className={`p-button-sm p-button-text lock-button ${
            field.frozen ? "pin-filled" : "pin-outlined"
          }`}
          onClick={(e) => {
            e.stopPropagation();
            if (field.frozen) {
              removeFromFrozen(field);
            } else {
              addToFrozen(field);
            }
          }}
        />

        {field.withDelete && (
          <Button
            type="button"
            icon={<DeleteOutlined />}
            className={`p-button-sm p-button-text lock-button delete-icon`}
            onClick={(e) => {
              e.stopPropagation();
              onColumnsDelete && onColumnsDelete(field.key);
            }}
          />
        )}
      </div>
    );
  };

  const rowClassName = (data) => {
    const status = data?.lbr > 30;
    const className = [];
    if (status) {
      className.push("row-red");
    }

    if (data?.disabled) {
      className.push("row-disabled");
    }
    if (withMultiplicity && isDisableSelection && !isDataSelectable(data)) {
      className.push("row-disabled");
    }
    if (customRowClassName) {
      const customClass = customRowClassName(data);
      className.push(customClass);
    }

    return className?.join(" ");
  };

  const onColumnOrder = (event) => {
    const dragIndex = event.dragIndex - 1;
    const dropIndex = event.dropIndex - 1;
    const updatedColumns = [...columns];
    const [movedColumn] = updatedColumns.splice(dragIndex, 1);
    updatedColumns.splice(dropIndex, 0, movedColumn);
    const newColumnsOrder = updatedColumns.map((column, index) => ({
      ...column,
      originalIndex: index,
      index: index,
    }));
    setColumns([...newColumnsOrder]);
    setAllColumnsRequest && setAllColumnsRequest([...newColumnsOrder]);
    detectChange && detectChange();
  };

  // function to handle column resize
  const onColumnResizeEnd = (event: DataTableColumnResizeEndEvent) => {
    const columnKey = event.column.props.columnKey;

    const newColumns = [...columns];
    const toUpdateColumn = newColumns.find(
      (column) => column.key === columnKey
    );
    toUpdateColumn.width = event.delta + Number(event.column.props.style.width);
    setColumns([...newColumns]);
    setAllColumnsRequest && setAllColumnsRequest([...newColumns]);
    detectChange && detectChange();
  };

  const removeFromAppliedFilters = (columnKey: string) => {
    const filters = appliedFilters.filter((key) => key !== columnKey);
    setAppliedFilters([...filters]);
  };

  const addToAppliedFilters = (columnKey: string) => {
    if (appliedFilters.indexOf(columnKey) === -1)
      setAppliedFilters([...appliedFilters, columnKey]);
  };

  // const initFilters = () => {
  //   const initialFilters = {};
  //   initialFilters["global"] = {
  //     value: null,
  //     matchMode: FilterMatchMode.CONTAINS,
  //   };

  //   initialColumns.forEach((column: IDataTableColumns) => {
  //     initialFilters[column.key] = {
  //       value: null,
  //       matchMode: column.isDate
  //         ? FilterMatchMode.DATE_IS
  //         : FilterMatchMode.CONTAINS,
  //     };
  //   });
  //   setFilters(initialFilters);
  //   detectChange && detectChange();
  //   // setGlobalFilterValue("");
  //   setAppliedFilters([]);
  // };

  // const clearFilter = () => {
  //   initFilters();
  // };

  // const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const value = e.target.value;
  //   const _filters = { ...filters };
  //   // @ts-ignore
  //   _filters["global"].value = value;
  //   setFilters(_filters);
  //   detectChange && detectChange();
  //   // setGlobalFilterValue(value);
  // };

  const exportExcel = () => {
    import("xlsx").then((xlsx) => {
      const worksheet = xlsx.utils.json_to_sheet(selected);
      const workbook = { Sheets: { data: worksheet }, SheetNames: ["data"] };
      const excelBuffer = xlsx.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });

      saveAsExcelFile(excelBuffer, excelFileName || "my_data");
    });
  };

  const saveAsExcelFile = (buffer, fileName) => {
    import("file-saver").then((module) => {
      if (module && module.default) {
        const EXCEL_TYPE =
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
        const EXCEL_EXTENSION = ".xlsx";
        const data = new Blob([buffer], {
          type: EXCEL_TYPE,
        });

        module.default.saveAs(
          data,
          fileName + "_export_" + new Date().getTime() + EXCEL_EXTENSION
        );
      }
    });
  };

  const onColumnToggle = (event) => {
    const selectedColumns = event.value;
    const orderedSelectedColumns = columns.map((col) => ({
      ...col,
      isHidden: !selectedColumns.some((sCol) => sCol.key === col.key),
    }));
    setColumns([...orderedSelectedColumns]);
    setAllColumnsRequest && setAllColumnsRequest([...orderedSelectedColumns]);
    detectChange && detectChange();
  };

  const renderHeader = () => {
    return (
      <TableHeader theme={theme}>
        {selectedActionsTemplate && (
          <div className="left">{selectedActionsTemplate}</div>
        )}
        <div className={`right ${alignLeft ? "" : "align-right"}`}>
          {extra && extra}
          {/* <span className="p-input-icon-left">
            <i className="pi pi-search" />
            <InputText
              value={filters?.global?.value || ""}
              onChange={onGlobalFilterChange}
              placeholder={t("Search...")}
            />
          </span> */}

          {/* {!noSelect && !singleSelection && (
            <ToggleButton
              onLabel={t("Do not sort")}
              offLabel={t("Show selected first")}
              // onIcon="pi pi-check"
              // offIcon="pi pi-times"
              checked={showSelectedFirst}
              onChange={(e) => setShowSelectedFirst(e.value)}
            />
          )} */}
          {/* <Button
            type="button"
            rounded
            className="primary-button"
            icon="pi pi-table"
            label={t("Columns")}
            onClick={(e) => overlayPanelRef.current.toggle(e)}
          /> */}
          {/* <Button
            type="button"
            rounded
            className="primary-button"
            icon="pi pi-filter-slash"
            label={t("Clear all")}
            disabled={appliedFilters.length === 0}
            onClick={clearFilter}
          /> */}
          {/* {extra && extra}   */}

          <OverlayPanel ref={overlayPanelRef} showCloseIcon>
            <MultiSelect
              value={columns.filter((cols) => !cols.isHidden)}
              options={columns}
              optionLabel="label"
              onChange={onColumnToggle}
              className="w-full hide-columns-overlay"
              display="chip"
              itemTemplate={(option) => t(option.label)}
            />
          </OverlayPanel>

          {withDelete && (
            <Button
              type="button"
              disabled={!selected?.length}
              label={t("Move to trash can")}
              icon="pi pi-trash"
              onClick={onDeleteClick}
              className="primary-button cancel-button"
              rounded
            />
          )}

          {!noDownload && (
            <Button
              type="button"
              disabled={!selected?.length}
              label={t("Download")}
              icon="pi pi-download"
              className="primary-button"
              onClick={exportExcel}
              rounded
            />
          )}

          {displaySaveCancel && (
            <>
              <Button
                rounded
                className="primary-button cancel-button"
                type="button"
                onClick={onCancelClick && onCancelClick}
              >
                {t("Cancel")}
              </Button>
              <Button
                rounded
                className="primary-button save-button"
                type="button"
                disabled={disableSave}
                loading={saveLoading}
                onClick={onSaveClick && onSaveClick}
              >
                {t("Save")}
              </Button>
            </>
          )}
        </div>
      </TableHeader>
    );
  };

  const header = renderHeader();

  const textEditor = (options) => {
    return (
      <InputText
        type="text"
        value={options.value}
        onChange={(e) => options.editorCallback(e.target.value)}
      />
    );
  };

  const booleanEditor = (options) => {
    return (
      <InputSwitch
        checked={options.value === "true"}
        onChange={(e) => options.editorCallback(String(e.target.value))}
      />
    );
  };

  const onRowEditComplete = (e) => {
    const _data = [...data];
    const { newData, index } = e;
    _data[index] = newData;
    onRowsEdit && onRowsEdit(e.newData);
  };

  const dateFilterTemplate = (options) => {
    return (
      <Calendar
        value={options.value}
        onChange={(e) => options.filterCallback(e.value, options.index)}
        dateFormat="yy/mm/dd"
        placeholder="yyyy/mm/dd"
      />
    );
  };

  const getSortedData = () => {
    if (noSelect || singleSelection || !data) return data;

    return [...data].sort((a, b) => {
      const aSelected = selected?.some((row) => row.id === a.id) as any;
      const bSelected = selected?.some((row) => row.id === b.id) as any;
      return bSelected - aSelected;
    });
  };

  const memoizedData = useMemo(
    () => getSortedData(),
    [data, showSelectedFirst]
  );
  // const [headerPanelShow, setHeaderPanelShow] = useState(
  //   defaultPanelExpand || false
  // );

  const [chunkData, setChunkData] = useState(
    Array.from({ length: data?.length })
  );

  useEffect(() => {
    setChunkData(Array.from({ length: data?.length }));
  }, [data?.length]);

  const loadLazyData = (event) => {
    if (data && event.last !== 0) {
      const _virtualData = [...chunkData];
      const { first, last } = event;
      const loadedCars = data.slice(first, last);

      Array.prototype.splice.apply(_virtualData, [
        ...[first, last - first],
        ...loadedCars,
      ]);

      setChunkData([..._virtualData]);
    }
  };

  const isDataSelectable = useCallback(
    (record) => {
      return selected?.some((_selected) => _selected.id === record?.data?.id);
    },
    [selected]
  );

  const isDisableSelection = useMemo(() => {
    const max = multiplicity?.split("..")[1];
    if (max === "n") {
      return false;
    }

    const newSelected = selected?.filter((_selected) => !!_selected?.id);
    if (!isNaN(Number(max)) && newSelected?.length >= Number(max)) {
      return true;
    }
    return false;
  }, [multiplicity, selected]);

  return (
    <Wrapper
      className="data-table-wrapper"
      style={{ width: loading || data?.length === 0 ? "100%" : "100%" }}
    >
      {/* {!(loading && (!data || data?.length === 0)) && (
          <ShowPanel
            theme={theme}
            style={{ top: headerPanelShow ? 60 : 0 }}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setHeaderPanelShow((prev) => !prev);
            }}
          >
            {headerPanelShow ? (
              <i className="pi pi-angle-up" />
            ) : (
              <i className="pi pi-angle-down" />
            )}
          </ShowPanel>
        )} */}
      {loading && (!data || data?.length === 0) ? (
        <div className="loader">
          <LoadingOutlined />
        </div>
      ) : data?.length === 0 ? (
        <div className="empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t(emptyMessage) || t("No Data")}
          />
        </div>
      ) : (
        <DataTable
          virtualScrollerOptions={
            noSelect || data?.length < 10 || !virtualScroll
              ? null
              : {
                  itemSize: 40,
                  lazy: lazyLoad,
                  onLazyLoad: loadLazyData,
                  showLoader: true,
                }
          }
          scrollable
          scrollHeight={height || "400px"}
          key={key}
          rowClassName={rowClassName}
          isDataSelectable={
            disableSelection
              ? disableSelection
              : withMultiplicity && isDisableSelection
              ? isDataSelectable
              : () => true
          }
          value={
            virtualScroll && data?.length > 10 && lazyLoad
              ? chunkData
              : memoizedData
          }
          reorderableColumns
          columnResizeMode="expand"
          onColReorder={onColumnOrder}
          selection={selected || []}
          className={`${columns.length === 0 && "empty-datatable"} ${
            readOnlySelect ? "readonly-select" : ""
          } ${hideSelectAll ? "hide-select-all" : ""}`}
          onSelectionChange={(e) => {
            setSelected && !readOnlySelect && setSelected(e.value);
          }}
          removableSort
          resizableColumns
          reorderableRows={rowReorder}
          showGridlines
          dataKey={dataKey || "id"}
          frozenWidth={"100px"}
          onRowEditComplete={onRowEditComplete}
          filters={filters}
          globalFilterFields={globalFilterFields}
          header={noHeader ? null : header}
          emptyMessage={t(emptyMessage) || t("No Data")}
          editMode="row"
          dragSelection={false}
          selectionMode={
            noSelect ? null : singleSelection ? "radiobutton" : "checkbox"
          }
          onColumnResizeEnd={onColumnResizeEnd}
          onRowReorder={(e) => onRowsEdit(e.value)}
          onFilter={(event) => {
            setFilters(event.filters);
            detectChange && detectChange();
          }}
          sortField={sort.field}
          sortOrder={sort.order}
          onSort={(event) => {
            setSort({
              field: event.sortField,
              order: event.sortOrder,
            });
            detectChange && detectChange();
          }}
          expandedRows={expandedRows}
          rowExpansionTemplate={rowExpansionTemplate}
          onRowExpand={onRowExpand}
          onRowCollapse={onRowCollapse}
        >
          {!noSelect && (
            <Column
              selectionMode={singleSelection ? "single" : "multiple"}
              frozen
              field="selectMultiple"
              headerStyle={{ width: "3rem", maxWidth: "3rem" }}
              reorderable={false}
            />
          )}

          {expandable && (
            <Column
              className="expandable"
              expander={(rowData) => {
                // If expandCondition is provided, use it
                if (expandCondition) {
                  return expandCondition(rowData);
                }

                // Use templatesData from component scope to prevent re-renders
                if (templatesData && rowData.templateId) {
                  const hasAttributes =
                    templatesData[rowData.templateId]?.attributeTemplates
                      ?.length > 0;
                  return hasAttributes;
                }

                // Fallback to the default expandable value
                return !!expandable;
              }}
              field="expandable"
              frozen
              style={{ width: "17px" }}
              reorderable={false}
            />
          )}

          {rowReorder && (
            <Column
              field="rowReorder"
              frozen
              rowReorder
              style={{ width: "3rem" }}
              reorderable={false}
            />
          )}

          {[...columns].map((field: IDataTableColumns) => {
            if (field.isHidden) {
              return null;
            }
            if (!field.key) {
              return null;
            }

            return (
              <Column
                sortable
                editor={
                  field.editable
                    ? (options) =>
                        options.value === "true" || options.value === "false"
                          ? booleanEditor(options)
                          : textEditor(options)
                    : null
                }
                field={field.key}
                key={field.key}
                dataType={field.isDate ? "date" : "text"}
                header={getColumnHeader(field)}
                filter
                alignFrozen="left"
                headerClassName={
                  appliedFilters.includes(field.key) ? "has-filters" : ""
                }
                filterField={field.key}
                onFilterApplyClick={(e) => {
                  addToAppliedFilters(e.field);
                }}
                onFilterClear={() => {
                  removeFromAppliedFilters(field.key);
                }}
                filterElement={field.isDate ? dateFilterTemplate : null}
                style={{
                  width: field.width,
                  minWidth: 100,
                }}
                frozen={field.frozen}
                columnKey={field.key}
                reorderable
                filterPlaceholder={t("Search...")}
                resizeable={!field.frozen}
                body={loading ? <Skeleton /> : field.render || null}
              />
            );
          })}
          {editable && (
            <Column
              field="roweditor"
              rowEditor
              frozen
              headerStyle={{ width: "10%", minWidth: "8rem" }}
              bodyStyle={{ textAlign: "center" }}
            ></Column>
          )}

          {extraAction && (
            <Column body={extraAction} style={{ minWidth: "8rem" }}></Column>
          )}
        </DataTable>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  /* overflow: auto; */
  width: fit-content;
  /* width: 100%; */
  position: relative;

  & .readonly-select tr {
    background-color: #fff !important;
  }
  & .row-disabled:not(.p-highlight) td {
    color: #a2a2a2;
  }
  & .hide-select-all {
    & th .p-checkbox {
      display: none;
    }
  }
  & .readonly-select .p-checkbox-box {
    background-color: white !important;
    border-width: 1px !important;
    & svg {
      color: #3b82f6;
    }
  }
  & .expandable {
    padding: 0px 0px 0px 8px !important;
    max-width: 30px;
  }
  & .empty {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  & .row-red {
    background-color: #e5c1c0;

    & .ant-tag {
      background: #9d2323;
      color: #fff;
      border: none;
    }
  }

  & .p-row-toggler {
    &:hover {
      background-color: transparent;
    }
  }
  & .loader {
    font-size: 30px;
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
  }
  & td {
    white-space: break-spaces;
    padding: 8px;
    font-size: 12px;
    color: black;
  }

  & .p-column-title {
    width: 100%;
  }

  & .p-selection-column .p-column-title {
    display: none;
  }
  & .column-header {
    /* position: relative; */
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: 400;
    padding-right: 26px;
  }

  & .p-checkbox-box {
    width: 18px;
    height: 18px;
    &:focus {
      box-shadow: none;
      border: 2px solid #d1d5db;
    }
  }

  & td a {
    color: #000;
    text-decoration: none;
  }

  /* & .p-datatable-table {
    width: fit-content;
  } */

  & .empty-datatable .p-datatable-table {
    width: 100%;
  }

  & .p-column-filter-menu-button-active,
  .p-column-filter-menu-button:hover {
    background-color: transparent;
  }
  & th.p-highlight {
    & .p-sortable-column-icon {
      color: red;
    }
  }

  & .p-column-filter-menu-button,
  .p-sortable-column-icon {
    color: var(--color-text);
  }

  & .p-checkbox > .p-highlight:focus {
    border-color: var(--color-text);
  }

  & .has-filters .p-column-filter-menu-button-active {
    color: red;
  }

  & .pin-outlined {
    color: var(--color-text);
  }
  & .pin-filled {
    color: red;
  }
  & .lock-button {
    position: absolute;
    right: 50px;
    width: fit-content;
    padding: 0px;
    top: 0px;
    box-shadow: none;
    bottom: 0px;
    font-size: 13px;
    &:hover {
      background-color: transparent;
    }
  }

  & .delete-icon {
    right: 70px;
    color: red;
  }

  & .ant-typography {
    font-family: Poppins, sans-serif;
    font-size: 13px;
    margin-bottom: 0px;
  }
  & th {
    padding: 3px 8px;
    font-size: 13px;
  }
`;

const TableHeader = styled.div<{ theme: any }>`
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;

  & .p-togglebutton {
    box-shadow: none !important;
    padding: 6px 10px;
    font-weight: 500;
    border-radius: 2rem;
  }

  & .p-highlight {
    background-color: ${({ theme }) => theme.colorPrimary};
    border-color: ${({ theme }) => theme.colorPrimary};
  }

  & .align-right {
    margin-left: auto;
  }
  & .left,
  .right {
    display: flex;
    /* justify-content: right; */
    gap: 10px;

    align-items: center;
    flex-wrap: wrap;
  }
  & button {
    padding: 6px 10px;
    &:focus {
      box-shadow: none;
    }
    &:disabled {
      cursor: not-allowed;
    }
  }

  & .p-inputtext {
    padding: 6px 7px 6px 34px;
    width: 100%;
  }
`;

// Custom comparison function for props
const arePropsEqual = (
  prevProps: IDataTableProps,
  nextProps: IDataTableProps
) => {
  // Return true if you want to skip re-render, false otherwise
  return (
    prevProps.data === nextProps.data &&
    prevProps.columns === nextProps.columns &&
    prevProps.loading === nextProps.loading &&
    prevProps.triggerChange === nextProps.triggerChange &&
    prevProps.selected === nextProps.selected &&
    prevProps.filters === nextProps.filters &&
    prevProps.sort === nextProps.sort &&
    prevProps.displaySaveCancel === nextProps.displaySaveCancel &&
    prevProps.expandedRows === nextProps.expandedRows &&
    prevProps.selectedActionsTemplate === nextProps.selectedActionsTemplate &&
    prevProps.multiplicity === nextProps.multiplicity
  );
};

const StyledDataTable = memo(MyTableComponent, arePropsEqual);

export { StyledDataTable };
